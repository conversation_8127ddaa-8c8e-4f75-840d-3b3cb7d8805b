# Create EmCentrix Azure Security Architecture PDF

Write-Host "Creating EmCentrix Azure Security Architecture PDF..." -ForegroundColor Green

try {
    # Start Word
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $word.DisplayAlerts = 0
    
    # Create new document
    $doc = $word.Documents.Add()
    $selection = $word.Selection
    
    # Title
    $selection.Font.Name = "Calibri"
    $selection.Font.Size = 20
    $selection.Font.Bold = $true
    $selection.TypeText("EmCentrix Azure Security Architecture")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Document info
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("Document Classification: Confidential")
    $selection.TypeParagraph()
    $selection.TypeText("Version: 2.1")
    $selection.TypeParagraph()
    $selection.TypeText("Date: June 2024")
    $selection.TypeParagraph()
    $selection.TypeText("Prepared for: Vendor Due Diligence")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Executive Summary
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Executive Summary")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("EmCentrix leverages Microsoft Azure's enterprise-grade cloud platform to deliver secure, scalable, and compliant customer communication solutions. Our Azure-based security architecture implements defense-in-depth principles with comprehensive threat protection, monitoring, and compliance capabilities.")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Azure Infrastructure Overview
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Infrastructure Overview")
    $selection.TypeParagraph()
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Core Azure Services")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• Compute: Azure Virtual Machines, Azure App Service, Azure Functions")
    $selection.TypeParagraph()
    $selection.TypeText("• Storage: Azure Blob Storage, Azure SQL Database, Azure Cosmos DB")
    $selection.TypeParagraph()
    $selection.TypeText("• Networking: Azure Virtual Network, Azure Load Balancer, Azure Application Gateway")
    $selection.TypeParagraph()
    $selection.TypeText("• Security: Azure Security Center, Azure Defender suite, Azure Key Vault")
    $selection.TypeParagraph()
    $selection.TypeText("• Monitoring: Azure Monitor, Application Insights, Azure Sentinel")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Geographic Distribution
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Geographic Distribution")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• Primary Region: East US 2")
    $selection.TypeParagraph()
    $selection.TypeText("• Secondary Region: West US 2")
    $selection.TypeParagraph()
    $selection.TypeText("• Backup Region: Central US")
    $selection.TypeParagraph()
    $selection.TypeText("• Data Residency: All customer data remains within the United States")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Azure Security Services
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Security Services Implementation")
    $selection.TypeParagraph()
    
    # Azure Defender for Cloud
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Defender for Cloud")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("Purpose: Comprehensive cloud security posture management and threat protection")
    $selection.TypeParagraph()
    $selection.TypeText("Key Features:")
    $selection.TypeParagraph()
    $selection.TypeText("• Continuous security assessment of all Azure resources")
    $selection.TypeParagraph()
    $selection.TypeText("• Vulnerability scanning for virtual machines and container images")
    $selection.TypeParagraph()
    $selection.TypeText("• Security recommendations and remediation guidance")
    $selection.TypeParagraph()
    $selection.TypeText("• Compliance dashboard for regulatory standards (SOC 2, ISO 27001, PCI DSS)")
    $selection.TypeParagraph()
    $selection.TypeText("• Just-in-time VM access controls")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Azure Defender for Endpoint
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Defender for Endpoint")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("Purpose: Advanced endpoint detection and response (EDR) capabilities")
    $selection.TypeParagraph()
    $selection.TypeText("Key Features:")
    $selection.TypeParagraph()
    $selection.TypeText("• Deployed on all Windows and Linux virtual machines")
    $selection.TypeParagraph()
    $selection.TypeText("• Real-time threat detection and automated response")
    $selection.TypeParagraph()
    $selection.TypeText("• Next-generation antivirus protection")
    $selection.TypeParagraph()
    $selection.TypeText("• Automated investigation and remediation")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Azure Key Vault
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Key Vault")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("Purpose: Secure storage and management of cryptographic keys, secrets, and certificates")
    $selection.TypeParagraph()
    $selection.TypeText("Key Features:")
    $selection.TypeParagraph()
    $selection.TypeText("• Hardware Security Module (HSM) backed key storage")
    $selection.TypeParagraph()
    $selection.TypeText("• Centralized secrets management for applications and services")
    $selection.TypeParagraph()
    $selection.TypeText("• FIPS 140-2 Level 2 validated HSMs")
    $selection.TypeParagraph()
    $selection.TypeText("• Role-based access control (RBAC)")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Data Protection
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Data Protection and Encryption")
    $selection.TypeParagraph()
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Encryption at Rest")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• Azure Storage Service Encryption: AES-256 encryption for all storage accounts")
    $selection.TypeParagraph()
    $selection.TypeText("• Azure SQL Database Transparent Data Encryption (TDE)")
    $selection.TypeParagraph()
    $selection.TypeText("• Azure Disk Encryption: BitLocker encryption for VM disks")
    $selection.TypeParagraph()
    $selection.TypeText("• Key Management: Azure Key Vault with customer-managed keys")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Backup and DR
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Backup and Disaster Recovery")
    $selection.TypeParagraph()
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Azure Backup Services")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• Daily Automated Backups: All critical systems and data")
    $selection.TypeParagraph()
    $selection.TypeText("• Geo-Redundant Storage (GRS): Cross-region backup replication")
    $selection.TypeParagraph()
    $selection.TypeText("• Point-in-Time Recovery: Granular recovery capabilities")
    $selection.TypeParagraph()
    $selection.TypeText("• Recovery Time Objective (RTO): 4 hours for critical systems")
    $selection.TypeParagraph()
    $selection.TypeText("• Recovery Point Objective (RPO): 1 hour for critical data")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Compliance
    $selection.Font.Size = 16
    $selection.Font.Bold = $true
    $selection.TypeText("Compliance and Governance")
    $selection.TypeParagraph()
    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• SOC 2 Type II: Annual third-party audit")
    $selection.TypeParagraph()
    $selection.TypeText("• ISO 27001: Information security management")
    $selection.TypeParagraph()
    $selection.TypeText("• PCI DSS: Payment card industry compliance")
    $selection.TypeParagraph()
    $selection.TypeText("• FedRAMP: Federal risk and authorization management")
    $selection.TypeParagraph()
    $selection.TypeParagraph()
    
    # Footer
    $selection.Font.Size = 10
    $selection.Font.Italic = $true
    $selection.TypeText("This document contains confidential and proprietary information of EmCentrix. Distribution is restricted to authorized personnel only.")
    
    # Save as PDF
    $pdfPath = "EmCentrix_Azure_Security_Architecture.pdf"
    Write-Host "Saving as PDF: $pdfPath" -ForegroundColor Yellow
    $doc.ExportAsFixedFormat($pdfPath, 17)
    
    # Close and cleanup
    $doc.Close()
    $word.Quit()
    
    if (Test-Path $pdfPath) {
        $pdfSize = (Get-Item $pdfPath).Length
        Write-Host "SUCCESS: PDF created: $pdfPath ($pdfSize bytes)" -ForegroundColor Green
    } else {
        throw "PDF file was not created"
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    try {
        if ($doc) { $doc.Close() }
        if ($word) { $word.Quit() }
    } catch { }
    exit 1
}

Write-Host "Azure Security Architecture PDF created successfully!" -ForegroundColor Green
