# Convert a single Word document to PDF
param(
    [string]$InputFile,
    [string]$OutputFile
)

try {
    Write-Host "Starting conversion of $InputFile"
    
    # Create Word application
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    
    # Open document
    $doc = $word.Documents.Open($InputFile)
    
    # Export as PDF
    $doc.ExportAsFixedFormat($OutputFile, 17)
    
    # Close document and application
    $doc.Close()
    $word.Quit()
    
    Write-Host "Successfully converted to $OutputFile" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Cleanup
    try {
        if ($doc) { $doc.Close() }
        if ($word) { $word.Quit() }
    } catch { }
}
