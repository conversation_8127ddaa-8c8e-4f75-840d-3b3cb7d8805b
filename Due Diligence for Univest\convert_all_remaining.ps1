# Convert all remaining Word documents to PDF
Write-Host "Converting all remaining Word documents to PDF..." -ForegroundColor Green

$basePath = "G:\AI Tasks\EmCentrix Policies"

# List of documents to convert (excluding the one we just converted)
$documents = @(
    "MPO_SPP_Asset_Management",
    "MPO_SPP_Change_Control_Management_Policy", 
    "MPO_SPP_Critical_Incident_Response_Team",
    "MPO_SPP_De-identifying_Data",
    "MPO_SPP_Disaster_Recovery_Test_Schedule",
    "MPO_SPP_Hardware_Sanitization",
    "MPO_SPP_Incident_Management_Policy_and_Procedure",
    "MPO_SPP_Information_Security_Training",
    "MPO_SPP_Password_Management_Policy_and_Procedure",
    "MPO_SPP_Patch_Management",
    "MPO_Spp_Physical_Security",
    "MPO_SPP_Risk_Assessment",
    "MPO_SPP_SDLC_For_Customization",
    "MPO_SPP_Secure_Software_Development_Lifecycle",
    "MPO_SPP_Sensitive_Data_Handling",
    "MPO_SPP_System_Hardening",
    "MPO_SPP_System_Monitoring",
    "MPO_SPP_Vendor_Management",
    "MPO_SPP_Visitor_Handling",
    "MPO_SPP_Vulnerability_Assessment",
    "MPO_System_Access",
    "Overview of EmCentrix's Information Security & Privacy Policies"
)

$successCount = 0
$errorCount = 0

foreach ($docName in $documents) {
    $inputFile = Join-Path $basePath "$docName.docx"
    $outputFile = Join-Path $basePath "$docName.pdf"
    
    # Check if input file exists and output doesn't exist
    if ((Test-Path $inputFile) -and (-not (Test-Path $outputFile))) {
        try {
            Write-Host "Converting: $docName" -ForegroundColor Cyan
            
            # Create Word application
            $word = New-Object -ComObject Word.Application
            $word.Visible = $false
            
            # Open document
            $doc = $word.Documents.Open($inputFile)
            
            # Export as PDF
            $doc.ExportAsFixedFormat($outputFile, 17)
            
            # Close document and application
            $doc.Close()
            $word.Quit()
            
            Write-Host "  ✓ Success: $docName.pdf" -ForegroundColor Green
            $successCount++
            
        } catch {
            Write-Host "  ✗ Error: $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
            
            # Cleanup
            try {
                if ($doc) { $doc.Close() }
                if ($word) { $word.Quit() }
            } catch { }
        }
    } else {
        if (-not (Test-Path $inputFile)) {
            Write-Host "  - Skipped: $docName.docx not found" -ForegroundColor Yellow
        } else {
            Write-Host "  - Skipped: $docName.pdf already exists" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n=== Conversion Summary ===" -ForegroundColor Yellow
Write-Host "Successfully converted: $successCount documents" -ForegroundColor Green
Write-Host "Errors encountered: $errorCount documents" -ForegroundColor Red
Write-Host "Conversion process completed!" -ForegroundColor Green
