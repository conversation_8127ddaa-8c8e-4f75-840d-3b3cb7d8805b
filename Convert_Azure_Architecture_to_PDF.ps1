# Convert EmCentrix Azure Security Architecture to PDF

Write-Host "Converting EmCentrix Azure Security Architecture to PDF..." -ForegroundColor Green

# Check if pandoc is available (alternative method)
$pandocAvailable = $false
try {
    $pandocVersion = pandoc --version 2>$null
    if ($pandocVersion) {
        $pandocAvailable = $true
        Write-Host "Pandoc found - using pandoc for conversion" -ForegroundColor Green
    }
} catch {
    Write-Host "Pandoc not available - using Word conversion method" -ForegroundColor Yellow
}

$sourceFile = "EmCentrix_Azure_Security_Architecture_PDF_Version.md"
$tempWordFile = "EmCentrix_Azure_Security_Architecture_Temp.docx"
$pdfFile = "EmCentrix_Azure_Security_Architecture.pdf"

if (-not (Test-Path $sourceFile)) {
    Write-Host "Error: Source file not found: $sourceFile" -ForegroundColor Red
    exit 1
}

if ($pandocAvailable) {
    # Use pandoc if available
    try {
        Write-Host "Converting using pandoc..." -ForegroundColor Yellow
        pandoc $sourceFile -o $pdfFile --pdf-engine=wkhtmltopdf --variable geometry:margin=1in
        
        if (Test-Path $pdfFile) {
            Write-Host "SUCCESS: PDF created using pandoc: $pdfFile" -ForegroundColor Green
            exit 0
        }
    } catch {
        Write-Host "Pandoc conversion failed, trying Word method..." -ForegroundColor Yellow
    }
}

# Use Word conversion method
try {
    Write-Host "Starting Microsoft Word..." -ForegroundColor Yellow
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $word.DisplayAlerts = 0
    
    Write-Host "Creating Word document from Markdown..." -ForegroundColor Yellow
    
    # Read the markdown content
    $content = Get-Content $sourceFile -Raw
    
    # Create a new document
    $doc = $word.Documents.Add()
    
    # Convert markdown-style formatting to Word formatting
    $content = $content -replace '^# (.+)$', '$1'  # Remove # from main headers
    $content = $content -replace '^## (.+)$', '$1'  # Remove ## from section headers
    $content = $content -replace '^### (.+)$', '$1'  # Remove ### from subsection headers
    $content = $content -replace '^\*\*(.+)\*\*', '$1'  # Remove ** from bold text
    $content = $content -replace '^- (.+)$', '• $1'  # Convert - to bullet points
    
    # Insert the content
    $doc.Content.Text = $content
    
    # Apply basic formatting
    $doc.Range().Font.Name = "Calibri"
    $doc.Range().Font.Size = 11
    
    # Format headers (this is a simplified approach)
    $doc.Range().Find.Execute("EmCentrix Azure Security Architecture")
    if ($doc.Range().Find.Found) {
        $doc.Range().Font.Size = 18
        $doc.Range().Font.Bold = $true
    }
    
    Write-Host "Saving as Word document..." -ForegroundColor Yellow
    $doc.SaveAs([System.IO.Path]::GetFullPath($tempWordFile))
    
    Write-Host "Converting to PDF..." -ForegroundColor Yellow
    $doc.ExportAsFixedFormat($pdfFile, 17)  # 17 = wdExportFormatPDF
    
    # Close document and Word
    $doc.Close()
    $word.Quit()
    
    # Clean up temp file
    if (Test-Path $tempWordFile) {
        Remove-Item $tempWordFile -Force
    }
    
    if (Test-Path $pdfFile) {
        $pdfSize = (Get-Item $pdfFile).Length
        Write-Host "SUCCESS: PDF created: $pdfFile ($pdfSize bytes)" -ForegroundColor Green
    } else {
        throw "PDF file was not created"
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    
    # Clean up
    try {
        if ($doc) { $doc.Close() }
        if ($word) { $word.Quit() }
        if (Test-Path $tempWordFile) { Remove-Item $tempWordFile -Force }
    } catch { }
    
    exit 1
}

Write-Host "Conversion completed successfully!" -ForegroundColor Green
