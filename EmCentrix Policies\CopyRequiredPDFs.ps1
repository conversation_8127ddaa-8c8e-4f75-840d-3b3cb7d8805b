# Copy Required PDF Documents to Due Diligence Folder

Write-Host "Copying required PDF documents to Due Diligence for Univest folder..." -ForegroundColor Green

# Set source and destination paths
$sourcePath = "."
$destPath = "..\Due Diligence for Univest"

# Check if destination folder exists
if (!(Test-Path $destPath)) {
    Write-Host "Error: Destination folder not found: $destPath" -ForegroundColor Red
    exit 1
}

# Define the files to copy with their new names
$filesToCopy = @{
    "MPO_High_Level_Information_Security_Policy.pdf" = "EmCentrix_Information_Security_Policy.pdf"
    "MPO_Access Control Policy.pdf" = "EmCentrix_Access_Control_Policy.pdf"
    "MPO_Breach_Notification_Policy.pdf" = "EmCentrix_Breach_Notification_Policy.pdf"
    "MPO_Business_Continuity_Plan.pdf" = "EmCentrix_Business_Continuity_Plan.pdf"
    "MPO_Data_Classification.pdf" = "EmCentrix_Data_Classification_Policy.pdf"
    "MPO_Data_Retention_Policy.pdf" = "EmCentrix_Data_Retention_Policy.pdf"
    "MPO_Encryption_Policy.pdf" = "EmCentrix_Encryption_Policy.pdf"
    "MPO_SPP_Risk_Assessment.pdf" = "EmCentrix_Risk_Assessment_Policy.pdf"
    "MPO_SPP_Vendor_Management.pdf" = "EmCentrix_Vendor_Management_Policy.pdf"
    "EmCentrix Disaster Recovery Plan 1.15 External.pdf" = "EmCentrix_Disaster_Recovery_Plan.pdf"
    "MPO_Incident_Response Procedures.pdf" = "EmCentrix_Incident_Response_Plan.pdf"
    "Overview of EmCentrix's Information Security & Privacy Policies.pdf" = "EmCentrix_Security_Privacy_Overview.pdf"
}

$successCount = 0
$errorCount = 0

Write-Host "Starting file copy process..." -ForegroundColor Yellow

foreach ($sourceFile in $filesToCopy.Keys) {
    $destFile = $filesToCopy[$sourceFile]
    $sourcePath_full = Join-Path $sourcePath $sourceFile
    $destPath_full = Join-Path $destPath $destFile
    
    try {
        if (Test-Path $sourcePath_full) {
            Copy-Item $sourcePath_full $destPath_full -Force
            Write-Host "SUCCESS: Copied $sourceFile -> $destFile" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "ERROR: Source file not found: $sourceFile" -ForegroundColor Red
            $errorCount++
        }
    } catch {
        Write-Host "ERROR: Failed to copy $sourceFile - $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host "`n=== COPY SUMMARY ===" -ForegroundColor Yellow
Write-Host "Files successfully copied: $successCount" -ForegroundColor Green
Write-Host "Errors encountered: $errorCount" -ForegroundColor Red

if ($successCount -gt 0) {
    Write-Host "`nFiles copied to: $destPath" -ForegroundColor Green
    Write-Host "Copy process completed!" -ForegroundColor Green
} else {
    Write-Host "`nNo files were copied." -ForegroundColor Yellow
}
