# PowerShell Script to Convert Word Documents to PDF
# This script converts all .docx files in the EmCentrix Policies folder to PDF format

# Set the source and destination folders
$sourceFolder = "EmCentrix Policies"
$destinationFolder = "EmCentrix Policies"  # Save PDFs in same folder as Word docs

# Check if Microsoft Word is installed
try {
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    Write-Host "Microsoft Word found. Starting conversion process..." -ForegroundColor Green
} catch {
    Write-Host "Microsoft Word is not installed or not accessible. Please install Microsoft Word to use this script." -ForegroundColor Red
    exit 1
}

# Get all .docx files from the source folder
$wordFiles = Get-ChildItem -Path $sourceFolder -Filter "*.docx" -File

if ($wordFiles.Count -eq 0) {
    Write-Host "No Word documents found in $sourceFolder" -ForegroundColor Yellow
    $word.Quit()
    exit 0
}

Write-Host "Found $($wordFiles.Count) Word documents to convert" -ForegroundColor Green

# Destination folder already exists (same as source folder)
Write-Host "Converting PDFs to same folder as Word documents: $destinationFolder" -ForegroundColor Green

# Convert each Word document to PDF
$convertedCount = 0
$errorCount = 0

foreach ($file in $wordFiles) {
    try {
        Write-Host "Converting: $($file.Name)" -ForegroundColor Cyan
        
        # Open the Word document
        $doc = $word.Documents.Open($file.FullName)
        
        # Create PDF filename
        $pdfName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name) + ".pdf"
        $pdfPath = Join-Path $destinationFolder $pdfName
        
        # Export as PDF
        $doc.ExportAsFixedFormat($pdfPath, 17) # 17 = PDF format
        
        # Close the document
        $doc.Close()
        
        Write-Host "✓ Converted: $pdfName" -ForegroundColor Green
        $convertedCount++
        
    } catch {
        Write-Host "✗ Error converting $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
        
        # Try to close the document if it's still open
        try {
            if ($doc) { $doc.Close() }
        } catch { }
    }
}

# Close Word application
$word.Quit()

# Summary
Write-Host "`n=== Conversion Summary ===" -ForegroundColor Yellow
Write-Host "Total files processed: $($wordFiles.Count)" -ForegroundColor White
Write-Host "Successfully converted: $convertedCount" -ForegroundColor Green
Write-Host "Errors encountered: $errorCount" -ForegroundColor Red

if ($convertedCount -gt 0) {
    Write-Host "`nPDF files have been saved to: $destinationFolder" -ForegroundColor Green
}

# List the newly converted files
if ($convertedCount -gt 0) {
    Write-Host "`nNewly converted PDF files:" -ForegroundColor Yellow
    Get-ChildItem -Path $destinationFolder -Filter "*.pdf" | Where-Object {
        $_.CreationTime -gt (Get-Date).AddMinutes(-5)
    } | ForEach-Object {
        Write-Host "  - $($_.Name)" -ForegroundColor White
    }
}

Write-Host "`nConversion process completed." -ForegroundColor Green
