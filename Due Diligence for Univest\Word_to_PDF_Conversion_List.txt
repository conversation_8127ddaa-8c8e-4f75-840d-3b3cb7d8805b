EmCentrix Policies - Word to PDF Conversion List

Date: December 2024
Purpose: Identify Word documents that need PDF conversion

WORD DOCUMENTS REQUIRING PDF CONVERSION

The following .docx files in the EmCentrix Policies folder do not have corresponding PDF versions and should be converted:

SUPPORTING POLICY PROCEDURES (MPO_SPP_):
1. MPO_SPP_Approved_Software.docx
2. MPO_SPP_Asset_Management.docx
3. MPO_SPP_Change_Control_Management_Policy.docx
4. MPO_SPP_Critical_Incident_Response_Team.docx
5. MPO_SPP_De-identifying_Data.docx
6. MPO_SPP_Disaster_Recovery_Test_Schedule.docx
7. MPO_SPP_Hardware_Sanitization.docx
8. MPO_SPP_Incident_Management_Policy_and_Procedure.docx
9. MPO_SPP_Information_Security_Training.docx
10. MPO_SPP_Password_Management_Policy_and_Procedure.docx
11. MPO_SPP_Patch_Management.docx
12. MPO_SPP_Risk_Assessment.docx
13. MPO_SPP_SDLC_For_Customization.docx
14. MPO_SPP_Secure_Software_Development_Lifecycle.docx
15. MPO_SPP_Sensitive_Data_Handling.docx
16. MPO_SPP_System_Hardening.docx
17. MPO_SPP_System_Monitoring.docx
18. MPO_SPP_Vendor_Management.docx
19. MPO_SPP_Visitor_Handling.docx
20. MPO_SPP_Vulnerability_Assessment.docx

OTHER POLICY DOCUMENTS:
21. MPO_Spp_Physical_Security.docx
22. MPO_System_Access.docx
23. Overview of EmCentrix's Information Security & Privacy Policies.docx

TOTAL: 23 Word documents need PDF conversion

DOCUMENTS ALREADY CONVERTED (Have both .docx and .pdf):
✓ EmCentrix Disaster Recovery Plan 1.15 External
✓ MPO_Acceptable_Use
✓ MPO_Access Control Policy
✓ MPO_BCP
✓ MPO_Breach_Notification_Policy
✓ MPO_Business_Continuity_Plan
✓ MPO_Data_Classification
✓ MPO_Data_Retention_Policy
✓ MPO_Dot Net Security Cheat Sheet
✓ MPO_Encryption_Policy
✓ MPO_High_Level_Information_Security_Policy
✓ MPO_Incident_Reporting
✓ MPO_Incident_Response Procedures
✓ MPO_Information_Ownership_Policy
✓ MPO_Information_Security_Charter
✓ MPO_Key_Management_Policy
✓ MPO_Microsoft SDL
✓ MPO_Pandemic_Policy
✓ MPO_Responsibilities_of_Information_Security_and_Compliance_Officer
✓ MPO_Sanction_Policy
✓ MPO_Secure Coding Checklist
✓ MPO_Secure Coding Guidelines
✓ MPO_Secure Coding Standards

CONVERSION METHODS

METHOD 1: PowerShell Script (Automated)
- Use the provided Convert_Word_to_PDF_Script.ps1
- Requires Microsoft Word to be installed
- Batch converts all documents automatically
- Run from PowerShell with: .\Convert_Word_to_PDF_Script.ps1

METHOD 2: Microsoft Word (Manual)
For each document:
1. Open the .docx file in Microsoft Word
2. Go to File → Export → Create PDF/XPS
3. Choose location: EmCentrix Policies folder (same folder as Word doc)
4. Click "Publish" to save as PDF

METHOD 3: Online Conversion Tools
- Upload .docx files to online converters
- Download PDF versions
- Note: Be cautious with sensitive documents

METHOD 4: PandaDoc (If Available)
- Upload Word documents to PandaDoc
- Use PandaDoc's conversion features
- Download as PDF format
- Note: Requires PandaDoc account and access

RECOMMENDED APPROACH

For the due diligence package, I recommend:

1. PRIORITY DOCUMENTS (Convert First):
   - MPO_SPP_Risk_Assessment.docx
   - MPO_SPP_Vulnerability_Assessment.docx
   - MPO_SPP_Vendor_Management.docx
   - MPO_SPP_System_Monitoring.docx
   - MPO_SPP_Incident_Management_Policy_and_Procedure.docx
   - Overview of EmCentrix's Information Security & Privacy Policies.docx

2. SUPPORTING DOCUMENTS (Convert as Needed):
   - All remaining MPO_SPP_ documents
   - Physical security and system access policies

3. QUALITY CONTROL:
   - Verify PDF formatting is correct
   - Ensure all pages are included
   - Check that headers/footers are preserved
   - Validate document readability

PANDADOC SPECIFIC INSTRUCTIONS

If using PandaDoc for conversion:

1. LOGIN TO PANDADOC:
   - Access your PandaDoc account
   - Navigate to document management section

2. UPLOAD DOCUMENTS:
   - Select "Upload" or "Import" option
   - Choose the Word documents from EmCentrix Policies folder
   - Upload in batches to avoid overwhelming the system

3. CONVERT TO PDF:
   - Select uploaded Word documents
   - Choose "Convert to PDF" or "Export as PDF" option
   - Configure PDF settings (quality, compression, etc.)

4. DOWNLOAD PDFS:
   - Download converted PDF files
   - Save to "EmCentrix Policies" folder (same location as Word docs)
   - Keep original file names with .pdf extension

5. VERIFY CONVERSION:
   - Open each PDF to verify formatting
   - Check that all content is preserved
   - Ensure document structure is maintained

ALTERNATIVE: BATCH CONVERSION SERVICES

If PandaDoc doesn't support batch conversion:

1. Adobe Acrobat Pro DC:
   - Batch convert multiple Word documents
   - Maintain formatting and structure
   - Professional PDF output

2. Microsoft 365 Online:
   - Upload to OneDrive or SharePoint
   - Use online Word to PDF conversion
   - Download converted files

3. Google Drive:
   - Upload Word documents
   - Open in Google Docs
   - Download as PDF

POST-CONVERSION CHECKLIST

After converting documents:
✓ Verify all 23 documents have been converted
✓ Check PDF formatting and readability
✓ Ensure file names are consistent
✓ Confirm PDFs are saved in EmCentrix Policies folder
✓ Update document inventory and checklist
✓ Test PDF accessibility and printing

FINAL DELIVERABLE STRUCTURE

After conversion, the EmCentrix Policies folder will contain:
- All original Word documents (.docx)
- All corresponding PDF versions (.pdf)
- Complete policy library in both formats

The Due Diligence folder will contain:
- All Word-friendly text documents created for due diligence
- Main due diligence response document
- Supporting analysis and summary documents

CONTACT FOR CONVERSION ASSISTANCE

If you need help with the conversion process:

IT Support Team  
Email: <EMAIL>  
Phone: [Phone Number]

Document Management Team  
Email: <EMAIL>  
Phone: [Phone Number]

---

This list provides a complete inventory of Word documents requiring PDF conversion and multiple methods to accomplish the task efficiently.
