# Convert Word Documents to PDF in Current Folder

Write-Host "Starting Word to PDF conversion..." -ForegroundColor Green

try {
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    Write-Host "Word application started." -ForegroundColor Green
} catch {
    Write-Host "Error: Microsoft Word not available." -ForegroundColor Red
    exit 1
}

$wordFiles = Get-ChildItem -Path "." -Filter "*.docx" | Where-Object {
    $pdfPath = $_.BaseName + ".pdf"
    -not (Test-Path $pdfPath)
}

Write-Host "Found $($wordFiles.Count) documents to convert." -ForegroundColor Cyan

if ($wordFiles.Count -eq 0) {
    Write-Host "All documents already have PDFs." -ForegroundColor Yellow
    $word.Quit()
    exit 0
}

$successCount = 0
$errorCount = 0

foreach ($file in $wordFiles) {
    try {
        Write-Host "Converting: $($file.Name)" -ForegroundColor White
        
        $doc = $word.Documents.Open($file.FullName)
        $pdfPath = $file.BaseName + ".pdf"
        $doc.ExportAsFixedFormat($pdfPath, 17)
        $doc.Close()
        
        Write-Host "Success: $($file.BaseName).pdf" -ForegroundColor Green
        $successCount++
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
        
        try { if ($doc) { $doc.Close() } } catch { }
    }
}

try {
    $word.Quit()
    Write-Host "Word application closed." -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not close Word." -ForegroundColor Yellow
}

Write-Host "=== Summary ===" -ForegroundColor Yellow
Write-Host "Processed: $($wordFiles.Count)" -ForegroundColor White
Write-Host "Success: $successCount" -ForegroundColor Green
Write-Host "Errors: $errorCount" -ForegroundColor Red

if ($successCount -gt 0) {
    Write-Host "Conversion completed!" -ForegroundColor Green
} else {
    Write-Host "No documents converted." -ForegroundColor Yellow
}
