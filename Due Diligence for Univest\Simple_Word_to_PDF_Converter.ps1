# Simple PowerShell Script to Convert Word Documents to PDF
# Converts all .docx files in EmCentrix Policies folder to PDF format

Write-Host "Starting Word to PDF conversion process..." -ForegroundColor Green

# Set the folder path
$folderPath = "..\EmCentrix Policies"

# Check if folder exists
if (!(Test-Path $folderPath)) {
    Write-Host "Error: EmCentrix Policies folder not found!" -ForegroundColor Red
    exit 1
}

# Try to create Word application
try {
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    Write-Host "Microsoft Word application started successfully." -ForegroundColor Green
} catch {
    Write-Host "Error: Microsoft Word is not installed or accessible." -ForegroundColor Red
    exit 1
}

# Get all Word documents that don't have corresponding PDFs
$wordFiles = Get-ChildItem -Path $folderPath -Filter "*.docx" | Where-Object {
    $pdfPath = Join-Path $folderPath ($_.BaseName + ".pdf")
    -not (Test-Path $pdfPath)
}

Write-Host "Found $($wordFiles.Count) Word documents to convert." -ForegroundColor Cyan

if ($wordFiles.Count -eq 0) {
    Write-Host "All Word documents already have corresponding PDF files." -ForegroundColor Yellow
    $word.Quit()
    exit 0
}

# Convert each document
$successCount = 0
$errorCount = 0

foreach ($file in $wordFiles) {
    try {
        Write-Host "Converting: $($file.Name)" -ForegroundColor White
        
        # Open document
        $doc = $word.Documents.Open($file.FullName)
        
        # Create PDF path
        $pdfPath = Join-Path $folderPath ($file.BaseName + ".pdf")
        
        # Export as PDF (17 = PDF format)
        $doc.ExportAsFixedFormat($pdfPath, 17)
        
        # Close document
        $doc.Close()
        
        Write-Host "  ✓ Success: $($file.BaseName).pdf" -ForegroundColor Green
        $successCount++
        
    } catch {
        Write-Host "  ✗ Error: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
        
        # Try to close document if still open
        try { if ($doc) { $doc.Close() } } catch { }
    }
}

# Close Word application
try {
    $word.Quit()
    Write-Host "Microsoft Word application closed." -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not properly close Word application." -ForegroundColor Yellow
}

# Summary
Write-Host "`n=== Conversion Summary ===" -ForegroundColor Yellow
Write-Host "Documents processed: $($wordFiles.Count)" -ForegroundColor White
Write-Host "Successfully converted: $successCount" -ForegroundColor Green
Write-Host "Errors encountered: $errorCount" -ForegroundColor Red

if ($successCount -gt 0) {
    Write-Host "`nPDF files saved to: $folderPath" -ForegroundColor Green
    Write-Host "Conversion completed successfully!" -ForegroundColor Green
} else {
    Write-Host "`nNo documents were converted." -ForegroundColor Yellow
}
